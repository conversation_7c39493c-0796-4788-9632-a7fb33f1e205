<template>
  <div class="matt-table-wrapper" :class="wrapperClass">
    <!-- 表格头部插槽区域 -->
    <div
      v-if="hasHeaderSlots"
      class="matt-table-header flex items-center justify-between p-4 border-b"
    >
      <!-- 左上角插槽 -->
      <div class="matt-table-header-left">
        <slot name="header-left"></slot>
      </div>

      <!-- 右上角插槽 -->
      <div class="matt-table-header-right flex items-center gap-2">
        <!-- 列设置功能 -->
        <DropdownMenu v-if="showColumnSettings">
          <DropdownMenuTrigger as-child>
            <Button variant="outline" size="sm" class="h-9 gap-2">
              <Settings class="h-4 w-4" />
              列设置
              <Badge variant="secondary" class="ml-1 h-5 px-1.5 text-xs">
                {{ visibleColumnsCount }}
              </Badge>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" class="w-56 z-[200]">
            <DropdownMenuLabel class="flex items-center justify-between">
              <span>选择显示的列</span>
              <span class="text-xs text-muted-foreground">
                {{ visibleColumnsCount }}/{{ columns.length }}
              </span>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <div class="max-h-64 overflow-y-auto">
              <DropdownMenuCheckboxItem
                v-for="column in columns"
                :key="column.key"
                :model-value="isColumnVisible(column.key)"
                :disabled="!isColumnHideable(column)"
                @update:model-value="checked => handleColumnToggle(column.key, checked)"
              >
                {{ column.title }}
                <span
                  v-if="!isColumnHideable(column)"
                  class="text-xs text-muted-foreground ml-auto"
                >
                  必需
                </span>
              </DropdownMenuCheckboxItem>
            </div>
            <DropdownMenuSeparator />
            <div class="p-2">
              <Button
                variant="ghost"
                size="sm"
                class="w-full justify-start text-xs"
                @click="resetColumnVisibility"
              >
                重置为默认
              </Button>
            </div>
          </DropdownMenuContent>
        </DropdownMenu>

        <!-- 自定义右上角插槽 -->
        <slot name="header-right"></slot>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="relative">
      <div class="absolute inset-0 bg-background/50 z-10 flex items-center justify-center">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
      </div>
    </div>

    <!-- 表格容器 -->
    <div class="relative w-full overflow-auto scrollbar" :style="tableContainerStyle">
      <table :class="tableClass">
        <!-- 表头 -->
        <thead :class="['sticky top-0 z-100', headerBgClass || 'bg-muted']">
          <tr class="border-b">
            <!-- 选择列 - 固定在左侧 -->
            <th
              v-if="rowSelection"
              class="h-12 px-4 text-center align-middle font-medium text-muted-foreground sticky left-0 z-30 bg-muted shadow-[2px_0_8px_-4px_rgba(0,0,0,0.12)]"
              style="width: 60px"
              @click="() => logger.info('表头th被点击')"
            >
              <Checkbox
                v-if="rowSelection.type === 'checkbox'"
                :checked="isAllSelected"
                :indeterminate="isIndeterminate"
                @click.stop="handleCheckboxClick"
              />
            </th>

            <!-- 数据列 -->
            <th
              v-for="column in visibleColumnsData"
              :key="column.key"
              :class="getHeaderClass(column)"
              :style="getColumnStyle(column)"
            >
              <div class="flex items-center justify-center gap-2">
                <span>{{ column.title }}</span>

                <!-- 排序图标 -->
                <div v-if="column.sortable" class="flex flex-col">
                  <ChevronUp
                    :class="[
                      'h-3 w-3 cursor-pointer transition-colors',
                      sortState[column.key] === 'asc'
                        ? 'text-primary'
                        : 'text-muted-foreground hover:text-foreground',
                    ]"
                    @click="handleSort(column.key, 'asc')"
                  />
                  <ChevronDown
                    :class="[
                      'h-3 w-3 cursor-pointer transition-colors -mt-1',
                      sortState[column.key] === 'desc'
                        ? 'text-primary'
                        : 'text-muted-foreground hover:text-foreground',
                    ]"
                    @click="handleSort(column.key, 'desc')"
                  />
                </div>
              </div>
            </th>

            <!-- 操作列 - 固定在右侧 -->
            <th
              v-if="showActionsColumn"
              class="h-12 px-4 text-center align-middle font-medium text-muted-foreground sticky right-0 z-30 bg-muted shadow-[-2px_0_8px_-4px_rgba(0,0,0,0.12)]"
              :style="actionsColumnStyle"
            >
              {{ actionsTitle || '操作' }}
            </th>
          </tr>
        </thead>

        <!-- 表体 -->
        <tbody>
          <!-- 空数据状态 -->
          <tr v-if="data.length === 0">
            <td :colspan="totalColumns" class="h-24 text-center text-muted-foreground">
              <div class="flex flex-col items-center justify-center gap-2">
                <div class="text-4xl">📋</div>
                <div>{{ emptyText || '暂无数据' }}</div>
              </div>
            </td>
          </tr>

          <!-- 数据行 -->
          <tr
            v-for="(record, index) in data"
            :key="getRowKey(record, index)"
            :class="getRowClass(record, index)"
            @click="handleRowClick(record, index)"
            @dblclick="handleRowDoubleClick(record, index)"
          >
            <!-- 选择列 - 固定在左侧 -->
            <td
              v-if="rowSelection"
              class="px-4 py-3 align-middle text-center sticky left-0 z-20 bg-muted shadow-[2px_0_8px_-4px_rgba(0,0,0,0.12)]"
              style="width: 60px"
              @click.stop
            >
              <Checkbox
                v-if="rowSelection.type === 'checkbox'"
                :model-value="isRowSelected(record, index)"
                :disabled="getCheckboxProps(record)?.disabled"
                @update:model-value="checked => handleRowSelect(record, index, !!checked)"
              />
              <input
                v-else
                type="radio"
                :name="'row-selection'"
                :checked="isRowSelected(record, index)"
                :disabled="getCheckboxProps(record)?.disabled"
                @change="() => handleRowSelect(record, index, true)"
                class="h-4 w-4 text-primary focus:ring-primary border-gray-300"
              />
            </td>

            <!-- 数据列 -->
            <td
              v-for="column in visibleColumnsData"
              :key="column.key"
              :class="getCellClass(column)"
              :style="getColumnStyle(column)"
            >
              <div :class="getCellContentClass(column)">
                <!-- 插槽内容 -->
                <slot
                  v-if="column.slot"
                  :name="column.key"
                  :record="record"
                  :index="index"
                  :value="getCellValue(record, column)"
                />

                <!-- 默认内容 -->
                <template v-else>
                  <Tooltip v-if="column.tooltip" :content="getCellDisplayValue(record, column)">
                    <span>{{ getCellDisplayValue(record, column) }}</span>
                  </Tooltip>

                  <div v-else-if="column.copyable" class="flex items-center gap-2">
                    <span>{{ getCellDisplayValue(record, column) }}</span>
                    <Button
                      variant="ghost"
                      size="icon"
                      class="h-6 w-6"
                      @click.stop="copyToClipboard(getCellDisplayValue(record, column))"
                    >
                      <Copy class="h-3 w-3" />
                    </Button>
                  </div>

                  <span v-else>{{ getCellDisplayValue(record, column) }}</span>
                </template>
              </div>
            </td>

            <!-- 操作列 - 固定在右侧 -->
            <td
              v-if="showActionsColumn"
              class="px-4 py-3 align-middle text-center sticky right-0 z-20 bg-muted shadow-[-2px_0_8px_-4px_rgba(0,0,0,0.12)]"
              :style="actionsColumnStyle"
            >
              <!-- 自定义操作插槽容器，确保内容居中 -->
              <div class="flex items-center justify-center">
                <slot name="actions" :record="record" :index="index">
                  <!-- 默认操作按钮 -->
                  <div
                    v-if="actions && actions.length > 0"
                    class="flex items-center justify-center gap-2"
                  >
                    <template
                      v-for="action in getVisibleActions(record, index)"
                      :key="action.title"
                    >
                      <Button
                        :variant="action.type === 'primary' ? 'default' : action.type || 'ghost'"
                        size="sm"
                        :disabled="isActionDisabled(action, record, index)"
                        @click.stop="handleActionClick(action, record, index)"
                      >
                        <component v-if="action.icon" :is="action.icon" class="h-4 w-4" />
                        {{ action.title }}
                      </Button>
                    </template>
                  </div>
                </slot>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, useSlots } from 'vue'
import { ChevronUp, ChevronDown, Copy, Settings } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Tooltip } from '@/components/ui/tooltip'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from '@/components/ui/dropdown-menu'
import { cn } from '@/lib/utils'
import { logger } from '@mattverse/shared'
import type { MattTableColumn, MattTableAction, MattTableProps, MattTableEmits } from './types'
import { toast } from 'vue-sonner'

type TableRecord = Record<string, any>

const props = withDefaults(defineProps<MattTableProps<TableRecord>>(), {
  bordered: false,
  striped: false,
  hoverable: true,
  size: 'default',
  loading: false,
  emptyText: '暂无数据',
  showActions: true,
  actionsTitle: '操作',
  actionsWidth: 120,
  headerBgClass: '',
  showColumnSettings: false,
  visibleColumns: () => [],
})

const emit = defineEmits<MattTableEmits<TableRecord>>()

// 获取插槽
const slots = useSlots()

// 排序状态
const sortState = ref<Record<string, 'asc' | 'desc' | null>>({})

// 选中状态
const selectedRowKeys = ref<(string | number)[]>(props.rowSelection?.selectedRowKeys || [])

// 列可见性状态
const internalVisibleColumns = ref<string[]>([])

// 初始化可见列
const initVisibleColumns = () => {
  if (props.visibleColumns && props.visibleColumns.length > 0) {
    // 使用外部传入的可见列
    internalVisibleColumns.value = [...props.visibleColumns]
  } else {
    // 使用默认可见列（所有 defaultVisible !== false 的列）
    const defaultVisible = props.columns
      .filter(column => column.defaultVisible !== false)
      .map(column => column.key)
    internalVisibleColumns.value = defaultVisible
  }
}

// 监听外部选中状态变化
watch(
  () => props.rowSelection?.selectedRowKeys,
  newKeys => {
    if (newKeys) {
      selectedRowKeys.value = [...newKeys]
    }
  },
  { immediate: true, deep: true }
)

// 监听列配置变化，重新初始化可见列
watch(() => props.columns, initVisibleColumns, { immediate: true })

// 监听外部可见列变化
watch(
  () => props.visibleColumns,
  newVisibleColumns => {
    if (newVisibleColumns && newVisibleColumns.length > 0) {
      internalVisibleColumns.value = [...newVisibleColumns]
    }
  },
  { immediate: true, deep: true }
)

// 计算属性
const wrapperClass = computed(() => cn('matt-table-wrapper', props.class))

const tableClass = computed(() =>
  cn('w-full caption-bottom text-sm', {
    'border border-border': props.bordered,
    'table-fixed': true, // 使用固定布局以精确控制列宽
  })
)

// 可见列数据
const visibleColumnsData = computed(() => {
  return props.columns.filter(column => internalVisibleColumns.value.includes(column.key))
})

// 可见列数量
const visibleColumnsCount = computed(() => {
  return internalVisibleColumns.value.length
})

// 是否有头部插槽
const hasHeaderSlots = computed(() => {
  return props.showColumnSettings || !!slots['header-left'] || !!slots['header-right']
})

const totalColumns = computed(() => {
  let count = visibleColumnsData.value.length
  if (props.rowSelection) count++
  if (showActionsColumn.value) count++
  return count
})

const showActionsColumn = computed(() => {
  return props.showActions && ((props.actions && props.actions.length > 0) || !!slots.actions)
})

const actionsColumnStyle = computed(() => {
  const style: Record<string, string> = {}
  if (props.actionsWidth) {
    style.width =
      typeof props.actionsWidth === 'number' ? `${props.actionsWidth}px` : props.actionsWidth
  }
  return style
})

const tableContainerStyle = computed(() => {
  const style: Record<string, string> = {}
  if (props.maxHeight) {
    style.maxHeight = typeof props.maxHeight === 'number' ? `${props.maxHeight}px` : props.maxHeight
  }
  return style
})

const isAllSelected = computed(() => {
  if (props.data.length === 0) return false

  // 获取所有可选择的行（排除禁用的行）
  const selectableRows = props.data.filter(record => {
    const checkboxProps = getCheckboxProps(record)
    return !checkboxProps?.disabled
  })

  if (selectableRows.length === 0) return false

  // 检查所有可选择的行是否都被选中
  const result = selectableRows.every(record => {
    const key = getRowKey(record, props.data.indexOf(record))
    return selectedRowKeys.value.includes(key)
  })

  return result
})

// 表头复选框点击处理
const handleCheckboxClick = () => {
  logger.info('表头复选框被点击，当前状态', { isAllSelected: isAllSelected.value })
  // 切换全选状态
  handleSelectAll(!isAllSelected.value)
}

const isIndeterminate = computed(() => {
  if (props.data.length === 0) return false

  // 获取所有可选择的行（排除禁用的行）
  const selectableRows = props.data.filter(record => {
    const checkboxProps = getCheckboxProps(record)
    return !checkboxProps?.disabled
  })

  if (selectableRows.length === 0) return false

  // 计算已选中的可选择行数量
  const selectedCount = selectableRows.filter(record => {
    const key = getRowKey(record, props.data.indexOf(record))
    return selectedRowKeys.value.includes(key)
  }).length

  return selectedCount > 0 && selectedCount < selectableRows.length
})

// 方法
const getRowKey = (record: TableRecord, index: number): string | number => {
  return record.id || record.key || index
}

const getHeaderClass = (column: MattTableColumn) =>
  cn(
    'h-12 px-4 align-middle font-medium text-muted-foreground',
    {
      'text-left': column.align === 'left',
      'text-right': column.align === 'right',
      'text-center': !column.align || column.align === 'center', // 默认居中，或明确设置为center
    },
    column.headerClass
  )

const getCellClass = (column: MattTableColumn) =>
  cn(
    'px-4 py-3 align-middle',
    {
      'text-left': column.align === 'left',
      'text-right': column.align === 'right',
      'text-center': !column.align || column.align === 'center', // 默认居中，或明确设置为center
    },
    column.cellClass
  )

const getCellContentClass = (column: MattTableColumn) =>
  cn({
    truncate: column.ellipsis,
    'whitespace-nowrap': !column.wrap,
    'whitespace-normal break-words': column.wrap,
  })

const getRowClass = (record: TableRecord, index: number) =>
  cn('border-b transition-colors', {
    'hover:bg-muted/50': props.hoverable,
    'bg-muted/25': props.striped && index % 2 === 1,
    'cursor-pointer': props.onRowClick,
  })

const getColumnStyle = (column: MattTableColumn) => {
  const style: Record<string, string> = {}

  if (column.width) {
    style.width = typeof column.width === 'number' ? `${column.width}px` : column.width
    style.maxWidth = style.width // 防止固定宽度列被拉伸
  } else {
    // 没有设置宽度的列，使用等分策略
    const columnsWithoutWidth = visibleColumnsData.value.filter(col => !col.width).length
    if (columnsWithoutWidth > 0) {
      // 计算固定宽度列占用的总宽度
      const fixedWidth = visibleColumnsData.value
        .filter(col => col.width)
        .reduce((total, col) => {
          const width = typeof col.width === 'number' ? col.width : parseInt(col.width || '0')
          return total + width
        }, 0)

      // 预留选择列和操作列的宽度
      let reservedWidth = fixedWidth
      if (props.rowSelection) reservedWidth += 60
      if (showActionsColumn.value) reservedWidth += (props.actionsWidth as number) || 120

      // 计算剩余宽度并等分
      style.width = `calc((100% - ${reservedWidth}px) / ${columnsWithoutWidth})`
      style.minWidth = '100px'
    }
  }

  if (column.minWidth) {
    style.minWidth = typeof column.minWidth === 'number' ? `${column.minWidth}px` : column.minWidth
  }

  return style
}

const getCellValue = (record: TableRecord, column: MattTableColumn) => {
  const keys = column.key.split('.')
  let value = record

  for (const key of keys) {
    value = value?.[key]
  }

  return value
}

const getCellDisplayValue = (record: TableRecord, column: MattTableColumn) => {
  const value = getCellValue(record, column)

  if (column.formatter) {
    return column.formatter(value, record, props.data.indexOf(record))
  }

  if (value === null || value === undefined) {
    return column.defaultValue || ''
  }

  return String(value)
}

const isRowSelected = (record: TableRecord, index: number): boolean => {
  const key = getRowKey(record, index)
  return selectedRowKeys.value.includes(key)
}

const getCheckboxProps = (record: TableRecord) => {
  return props.rowSelection?.getCheckboxProps?.(record) || {}
}

const getVisibleActions = (record: TableRecord, index: number) => {
  return (
    props.actions?.filter(action => (action.visible ? action.visible(record, index) : true)) || []
  )
}

const isActionDisabled = (action: MattTableAction, record: TableRecord, index: number): boolean => {
  return action.disabled ? action.disabled(record, index) : false
}

// 事件处理
const handleSort = (key: string, order: 'asc' | 'desc') => {
  if (sortState.value[key] === order) {
    sortState.value[key] = null
    emit('sort-change', key, null)
  } else {
    sortState.value = { [key]: order }
    emit('sort-change', key, order)
  }
}

const handleRowClick = (record: TableRecord, index: number) => {
  // 如果有行选择功能，点击行时切换选中状态
  if (props.rowSelection && props.rowSelection.type === 'checkbox') {
    const currentSelected = isRowSelected(record, index)
    handleRowSelect(record, index, !currentSelected)
  }

  emit('row-click', record, index)
}

const handleRowDoubleClick = (record: TableRecord, index: number) => {
  emit('row-double-click', record, index)
}

const handleSelectAll = (checked: boolean) => {
  logger.info('handleSelectAll 被调用', { checked })

  let newSelectedKeys: (string | number)[] = []

  if (checked) {
    // 全选：只选择未禁用的行
    newSelectedKeys = props.data
      .filter(record => {
        const checkboxProps = getCheckboxProps(record)
        return !checkboxProps?.disabled
      })
      .map(record => getRowKey(record, props.data.indexOf(record)))
  }

  // 更新内部状态
  selectedRowKeys.value = newSelectedKeys

  // 直接基于更新后的 selectedRowKeys 计算选中的行，避免异步更新问题
  const selectedRows = props.data.filter((record, index) => {
    const key = getRowKey(record, index)
    return newSelectedKeys.includes(key)
  })

  // 通知外部状态变化
  props.rowSelection?.onChange?.(newSelectedKeys, selectedRows)
  emit('selection-change', newSelectedKeys, selectedRows)
}

const handleRowSelect = (record: TableRecord, index: number, checked: boolean) => {
  const key = getRowKey(record, index)
  let newSelectedKeys: (string | number)[] = []

  if (props.rowSelection?.type === 'radio') {
    newSelectedKeys = checked ? [key] : []
  } else {
    newSelectedKeys = [...selectedRowKeys.value]
    if (checked) {
      if (!newSelectedKeys.includes(key)) {
        newSelectedKeys.push(key)
      }
    } else {
      const keyIndex = newSelectedKeys.indexOf(key)
      if (keyIndex > -1) {
        newSelectedKeys.splice(keyIndex, 1)
      }
    }
  }

  // 更新内部状态
  selectedRowKeys.value = newSelectedKeys

  // 直接基于更新后的 selectedRowKeys 计算选中的行
  const selectedRows = props.data.filter((record, index) => {
    const key = getRowKey(record, index)
    return newSelectedKeys.includes(key)
  })

  // 通知外部状态变化
  props.rowSelection?.onChange?.(newSelectedKeys, selectedRows)
  emit('selection-change', newSelectedKeys, selectedRows)
}

const handleActionClick = (action: MattTableAction, record: TableRecord, index: number) => {
  if (action.confirm) {
    if (confirm(action.confirm)) {
      action.onClick(record, index)
    }
  } else {
    action.onClick(record, index)
  }
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    // 这里可以添加成功提示
    toast.info(`内容 ${text} 已复制到剪贴板`)
  } catch (err) {
    logger.error('复制失败:', err)
  }
}

// 列设置相关方法
const isColumnVisible = (columnKey: string): boolean => {
  return internalVisibleColumns.value.includes(columnKey)
}

const isColumnHideable = (column: MattTableColumn): boolean => {
  return column.hideable !== false
}

const handleColumnToggle = (columnKey: string, checked: boolean) => {
  // 查找对应的列配置
  const column = props.columns.find(col => col.key === columnKey)

  // 如果是不可隐藏的列，不允许取消勾选
  if (!checked && column && !isColumnHideable(column)) {
    return
  }

  if (checked) {
    if (!internalVisibleColumns.value.includes(columnKey)) {
      internalVisibleColumns.value.push(columnKey)
    }
  } else {
    const index = internalVisibleColumns.value.indexOf(columnKey)
    if (index > -1) {
      internalVisibleColumns.value.splice(index, 1)
    }
  }

  // 通知外部状态变化
  emit('column-visibility-change', [...internalVisibleColumns.value])
  props.onColumnVisibilityChange?.([...internalVisibleColumns.value])
}

const resetColumnVisibility = () => {
  // 重置为默认可见列
  internalVisibleColumns.value = props.columns
    .filter(column => column.defaultVisible !== false)
    .map(column => column.key)

  // 通知外部状态变化
  emit('column-visibility-change', [...internalVisibleColumns.value])
  props.onColumnVisibilityChange?.([...internalVisibleColumns.value])
}
</script>

<style scoped>
.matt-table-wrapper {
  @apply rounded-md border;
}

.matt-table-wrapper table {
  @apply border-collapse;
  min-width: 100%;
}

.matt-table-wrapper th {
  @apply border-b border-border;
}

.matt-table-wrapper td {
  @apply border-b border-border;
}

.matt-table-wrapper tr:last-child td {
  @apply border-b-0;
}

/* 使用固定表格布局以精确控制列宽 */
.matt-table-wrapper table {
  table-layout: fixed;
}

/* 默认单元格样式 */
.matt-table-wrapper th,
.matt-table-wrapper td {
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 允许内容换行的列 */
.matt-table-wrapper .whitespace-normal {
  white-space: normal;
  word-wrap: break-word;
}

/* 不换行的列 */
.matt-table-wrapper .whitespace-nowrap {
  white-space: nowrap;
}

/* 固定表头样式 */
.matt-table-wrapper thead.sticky {
  position: sticky;
  top: 0;
  z-index: 10;
}

.matt-table-wrapper thead.sticky {
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* 表格容器滚动优化 */
.matt-table-wrapper .relative.w-full.overflow-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
}

.matt-table-wrapper .relative.w-full.overflow-auto::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.matt-table-wrapper .relative.w-full.overflow-auto::-webkit-scrollbar-track {
  background: transparent;
}

.matt-table-wrapper .relative.w-full.overflow-auto::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 3px;
}

.matt-table-wrapper .relative.w-full.overflow-auto::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.7);
}

/* 表格头部样式 */
.matt-table-header {
  background-color: hsl(var(--background));
  border-bottom: 1px solid hsl(var(--border));
}

.matt-table-header-left {
  flex: 1;
}

.matt-table-header-right {
  flex-shrink: 0;
}
</style>
