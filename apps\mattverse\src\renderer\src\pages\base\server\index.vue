<template>
  <div class="p-4 space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold">服务器状态</h1>
    </div>

    <!-- 搜索表单 -->
    <MattSearchForm
      :fields="searchFields"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 服务器列表表格 -->
    <MattTable
      :columns="tableColumns"
      :data="paginatedData"
      :loading="loading"
      :actions="tableActions"
      class="min-h-[400px]"
    >
      <!-- 表格右上角插槽：刷新间隔设置 -->
      <template #header-right>
        <div class="flex items-center gap-2">
          <Label class="text-sm text-muted-foreground whitespace-nowrap">刷新间隔（秒）</Label>
          <Input
            v-model.number="refreshInterval"
            type="number"
            min="1"
            max="300"
            class="w-20 h-9"
            @change="handleRefreshIntervalChange"
          />
          <Button
            variant="outline"
            size="sm"
            class="h-9 gap-2"
            @click="handleRefresh"
            :disabled="loading"
          >
            <MattIcon name="RefreshCw" class="h-4 w-4" />
            刷新
          </Button>
        </div>
      </template>

      <!-- 服务器名称列 - 支持复制 -->
      <template #serverName="{ record }">
        <div class="flex items-center gap-2">
          <span>{{ record.serverName }}</span>
          <Button
            variant="ghost"
            size="icon"
            class="h-6 w-6"
            @click.stop="copyToClipboard(record.serverName)"
          >
            <MattIcon name="Copy" class="h-3 w-3" />
          </Button>
        </div>
      </template>

      <!-- 服务器类型列 - 支持复制 -->
      <template #serverType="{ record }">
        <div class="flex items-center gap-2">
          <Badge :variant="getServerTypeBadgeVariant(record.serverType)">
            {{ getServerTypeText(record.serverType) }}
          </Badge>
          <Button
            variant="ghost"
            size="icon"
            class="h-6 w-6"
            @click.stop="copyToClipboard(record.serverType)"
          >
            <MattIcon name="Copy" class="h-3 w-3" />
          </Button>
        </div>
      </template>

      <!-- 服务器ID列 - 支持复制 -->
      <template #serverId="{ record }">
        <div class="flex items-center gap-2">
          <span class="font-mono text-xs">{{ record.serverId }}</span>
          <Button
            variant="ghost"
            size="icon"
            class="h-6 w-6"
            @click.stop="copyToClipboard(record.serverId)"
          >
            <MattIcon name="Copy" class="h-3 w-3" />
          </Button>
        </div>
      </template>

      <!-- 服务器状态列 -->
      <template #serverStatus="{ record }">
        <Badge :class="getStatusClass(record.serverStatus)">
          {{ getStatusText(record.serverStatus) }}
        </Badge>
      </template>

      <!-- 注册时间列 -->
      <template #createTime="{ record }">
        <span class="text-sm">{{ formatTimestamp(record.createTime) }}</span>
      </template>

      <!-- 更新时间列 -->
      <template #updateTime="{ record }">
        <span class="text-sm">{{ formatTimestamp(record.updateTime) }}</span>
      </template>

      <!-- 操作列 -->
      <template #actions="{ record }">
        <DropdownMenu>
          <DropdownMenuTrigger as-child>
            <Button variant="ghost" size="sm" class="h-8 w-8 p-0">
              <MattIcon name="MoreHorizontal" class="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" class="w-40">
            <DropdownMenuItem
              @click="handleServerAction('close', record)"
              :disabled="record.serverStatus === 'Stopped'"
            >
              <MattIcon name="Power" class="mr-2 h-4 w-4" />
              关闭
            </DropdownMenuItem>
            <DropdownMenuItem
              @click="handleServerAction('restart', record)"
              :disabled="record.serverStatus === 'Stopped'"
            >
              <MattIcon name="RotateCcw" class="mr-2 h-4 w-4" />
              重启
            </DropdownMenuItem>
            <DropdownMenuItem
              @click="handleServerAction('pause', record)"
              :disabled="record.serverStatus === 'Stopped'"
            >
              <MattIcon name="Pause" class="mr-2 h-4 w-4" />
              暂停
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              @click="handleServerAction('delete', record)"
              class="text-destructive focus:text-destructive"
            >
              <MattIcon name="Trash2" class="mr-2 h-4 w-4" />
              删除
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </template>
    </MattTable>

    <!-- 分页组件 -->
    <MattPagination
      v-model:current="currentPage"
      v-model:page-size="pageSize"
      :total="filteredData.length"
      :show-size-changer="true"
      :show-quick-jumper="true"
      @change="handlePageChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { toast } from 'vue-sonner'
import { useServerStore } from '@/store/modules/base/server'
import { logger } from '@mattverse/shared'
import type { MattTableColumn, MattTableAction } from '@mattverse/mattverse-ui'
import type { MattSearchFormField } from '@mattverse/mattverse-ui'
import type { Server } from '@mattverse/shared'

// 使用 store
const serverStore = useServerStore()

// 响应式数据
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const refreshInterval = ref(serverStore.refreshTimer)
const refreshTimer = ref<NodeJS.Timeout | null>(null)

// 搜索表单数据
const searchForm = ref({
  keyword: '', // 搜索关键词（服务器名称、类型、ID）
  serverType: '', // 服务器类型筛选
  serverStatus: '', // 服务器状态筛选
})

// 搜索表单字段配置
const searchFields = computed<MattSearchFormField[]>(() => [
  {
    name: 'keyword',
    type: 'input',
    label: '搜索',
    placeholder: '搜索服务器名称、类型或ID',
    icon: 'search',
    width: 'w-full sm:min-w-[280px] sm:max-w-[320px]',
  },
  {
    name: 'serverType',
    type: 'select',
    label: '服务器类型',
    placeholder: '选择服务器类型',
    options: [
      { label: '全部类型', value: '' },
      { label: 'Agent服务器', value: 'agentServer' },
      { label: 'ML服务器', value: 'mlServer' },
      { label: 'Batt服务器', value: 'battServer' },
    ],
    width: 'w-full sm:min-w-[160px] sm:max-w-[200px]',
  },
  {
    name: 'serverStatus',
    type: 'select',
    label: '服务器状态',
    placeholder: '选择服务器状态',
    options: [
      { label: '全部状态', value: '' },
      { label: '正常运行', value: 'Running' },
      { label: '已停止', value: 'Stopped' },
      { label: '已过期', value: 'Expired' },
      { label: '服务器超载', value: 'Overloaded' },
      { label: '原始状态', value: 'Stay' },
    ],
    width: 'w-full sm:min-w-[160px] sm:max-w-[200px]',
  },
])

// 表格列配置
const tableColumns = computed<MattTableColumn[]>(() => [
  {
    key: 'serverName',
    title: '服务器名称',
    width: 150,
    slot: true,
    align: 'center',
  },
  {
    key: 'serverType',
    title: '服务器类型',
    width: 120,
    slot: true,
    align: 'center',
  },
  {
    key: 'serverId',
    title: '服务器ID',
    width: 200,
    slot: true,
    align: 'center',
  },
  {
    key: 'url',
    title: '服务器地址',
    width: 200,
    align: 'center',
  },
  {
    key: 'serverStatus',
    title: '服务器状态',
    width: 120,
    slot: true,
    align: 'center',
  },
  {
    key: 'region',
    title: '所在地区',
    width: 100,
    align: 'center',
  },
  {
    key: 'version',
    title: '服务器版本',
    width: 150,
    align: 'center',
  },
  {
    key: 'createTime',
    title: '注册时间',
    width: 150,
    slot: true,
    align: 'center',
  },
  {
    key: 'updateTime',
    title: '更新时间',
    width: 150,
    slot: true,
    align: 'center',
  },
])

// 表格操作配置
const tableActions = computed<MattTableAction[]>(() => [])

// 计算属性
const filteredData = computed(() => {
  let data = serverStore.servers

  // 关键词搜索
  if (searchForm.value.keyword) {
    const keyword = searchForm.value.keyword.toLowerCase()
    data = data.filter(
      server =>
        server.serverName.toLowerCase().includes(keyword) ||
        server.serverType.toLowerCase().includes(keyword) ||
        server.serverId.toLowerCase().includes(keyword)
    )
  }

  // 服务器类型筛选
  if (searchForm.value.serverType) {
    data = data.filter(server => server.serverType === searchForm.value.serverType)
  }

  // 服务器状态筛选
  if (searchForm.value.serverStatus) {
    data = data.filter(server => server.serverStatus === searchForm.value.serverStatus)
  }

  return data
})

const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredData.value.slice(start, end)
})

// 工具方法
const getServerTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    agentServer: 'Agent服务器',
    mlServer: 'ML服务器',
    battServer: 'Batt服务器',
  }
  return typeMap[type] || type
}

const getServerTypeBadgeVariant = (
  type: string
): 'default' | 'secondary' | 'outline' | 'destructive' => {
  const variantMap: Record<string, 'default' | 'secondary' | 'outline' | 'destructive'> = {
    agentServer: 'default',
    mlServer: 'secondary',
    battServer: 'outline',
  }
  return variantMap[type] || 'default'
}

const { getStatusText, getStatusClass } = serverStore

const formatTimestamp = (timestamp: string) => {
  if (!timestamp) return '-'
  const date = new Date(parseInt(timestamp) * 1000)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    toast.success(`已复制: ${text}`)
  } catch (err) {
    logger.error('复制失败:', err)
    toast.error('复制失败')
  }
}

// 事件处理方法
const handleSearch = (formData: Record<string, any>) => {
  searchForm.value = {
    keyword: formData.keyword || '',
    serverType: formData.serverType || '',
    serverStatus: formData.serverStatus || '',
  }
  currentPage.value = 1 // 重置到第一页
}

const handleReset = () => {
  searchForm.value = {
    keyword: '',
    serverType: '',
    serverStatus: '',
  }
  currentPage.value = 1
}

const handlePageChange = (page: number, size: number) => {
  currentPage.value = page
  pageSize.value = size
}

const handleRefreshIntervalChange = () => {
  serverStore.setRefreshTimer(refreshInterval.value)
  setupAutoRefresh()
}

const handleRefresh = async () => {
  loading.value = true
  try {
    await serverStore.updateServerList()
    toast.success('服务器列表已刷新')
  } catch (error) {
    logger.error('刷新服务器列表失败:', error)
    toast.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const handleServerAction = async (action: string, server: Server) => {
  const actionMap: Record<string, string> = {
    close: '关闭',
    restart: '重启',
    pause: '暂停',
    delete: '删除',
  }

  const actionName = actionMap[action] || action

  // 删除操作需要确认
  if (action === 'delete') {
    const confirmed = confirm(`确定要删除服务器 "${server.serverName}" 吗？此操作不可撤销。`)
    if (!confirmed) return
  }

  try {
    // 这里应该调用相应的 API 来执行服务器操作
    // 由于没有具体的 API 接口，这里只是模拟
    toast.info(`正在${actionName}服务器: ${server.serverName}`)

    // 模拟 API 调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 刷新服务器列表
    await serverStore.updateServerList()

    toast.success(`${actionName}服务器成功: ${server.serverName}`)
  } catch (error) {
    logger.error(`${actionName}服务器失败:`, error)
    toast.error(`${actionName}服务器失败`)
  }
}

// 自动刷新设置
const setupAutoRefresh = () => {
  // 清除现有定时器
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }

  // 设置新的定时器
  if (refreshInterval.value > 0) {
    refreshTimer.value = setInterval(() => {
      handleRefresh()
    }, refreshInterval.value * 1000)
  }
}

// 监听刷新间隔变化
watch(
  () => refreshInterval.value,
  () => {
    serverStore.setRefreshTimer(refreshInterval.value)
    setupAutoRefresh()
  }
)

// 生命周期钩子
onMounted(async () => {
  // 初始化加载服务器列表
  await handleRefresh()

  // 设置自动刷新
  setupAutoRefresh()
})

onUnmounted(() => {
  // 清理定时器
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
})
</script>

<style lang="scss" scoped>
// 自定义样式
.server-status-page {
  .search-form {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 8px;
  }

  .server-table {
    .server-id {
      font-family: 'Courier New', monospace;
      font-size: 12px;
    }

    .status-badge {
      font-weight: 500;
    }
  }
}
</style>
